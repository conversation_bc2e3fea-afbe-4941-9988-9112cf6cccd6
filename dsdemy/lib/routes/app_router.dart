import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../screens/login_screen.dart';
import '../screens/signup_screen.dart';
import '../screens/user_profile_screen.dart';
import '../screens/update_profile_screen.dart';
import '../screens/courses_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/login',
    routes: [
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        name: 'signup',
        builder: (context, state) => const SignUpScreen(),
      ),
      // TODO: Add routes for other screens
      // User Profile
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const UserProfileScreen(),
      ),
      // Update Profile
      GoRoute(
        path: '/update-profile',
        name: 'update-profile',
        builder: (context, state) => const UpdateProfileScreen(),
      ),
      // Update Avatar
      GoRoute(
        path: '/update-avatar',
        name: 'update-avatar',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Update Avatar Screen - To be implemented')),
        ),
      ),
      // Update Password
      GoRoute(
        path: '/update-password',
        name: 'update-password',
        builder: (context, state) => const Scaffold(
          body:
              Center(child: Text('Update Password Screen - To be implemented')),
        ),
      ),
      // Course List
      GoRoute(
        path: '/courses',
        name: 'courses',
        builder: (context, state) => const CoursesScreen(),
      ),
      // Course Detail
      GoRoute(
        path: '/course/:id',
        name: 'course-detail',
        builder: (context, state) {
          final courseId = state.pathParameters['id'] ?? '';
          return Scaffold(
            body: Center(
                child: Text(
                    'Course Detail Screen for $courseId - To be implemented')),
          );
        },
      ),
      // Course Learning
      GoRoute(
        path: '/course/:id/learn',
        name: 'course-learn',
        builder: (context, state) {
          final courseId = state.pathParameters['id'] ?? '';
          return Scaffold(
            body: Center(
                child: Text(
                    'Course Learning Screen for $courseId - To be implemented')),
          );
        },
      ),
      // Create Course
      GoRoute(
        path: '/create-course',
        name: 'create-course',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Create Course Screen - To be implemented')),
        ),
      ),
      // Create Lesson
      GoRoute(
        path: '/create-lesson',
        name: 'create-lesson',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Create Lesson Screen - To be implemented')),
        ),
      ),
      // Quiz
      GoRoute(
        path: '/quiz/:id',
        name: 'quiz',
        builder: (context, state) {
          final quizId = state.pathParameters['id'] ?? '';
          return Scaffold(
            body: Center(
                child: Text('Quiz Screen for $quizId - To be implemented')),
          );
        },
      ),
      // Discussion
      GoRoute(
        path: '/discussion/:id',
        name: 'discussion',
        builder: (context, state) {
          final discussionId = state.pathParameters['id'] ?? '';
          return Scaffold(
            body: Center(
                child: Text(
                    'Discussion Screen for $discussionId - To be implemented')),
          );
        },
      ),
      // Rating
      GoRoute(
        path: '/rating/:id',
        name: 'rating',
        builder: (context, state) {
          final courseId = state.pathParameters['id'] ?? '';
          return Scaffold(
            body: Center(
                child: Text('Rating Screen for $courseId - To be implemented')),
          );
        },
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('Error: ${state.error}'),
      ),
    ),
  );
}
