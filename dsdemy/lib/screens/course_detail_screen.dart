import 'package:flutter/material.dart';
import 'package:dsdemy/theme/app_theme.dart';
import 'package:go_router/go_router.dart';

class CourseDetailScreen extends StatefulWidget {
  final String courseId;

  const CourseDetailScreen({
    super.key,
    required this.courseId,
  });

  @override
  State<CourseDetailScreen> createState() => _CourseDetailScreenState();
}

class _CourseDetailScreenState extends State<CourseDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late Map<String, dynamic> _courseData;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // In a real app, this would fetch the course data from an API
    // For now, we'll use dummy data
    _courseData = _getDummyCourseData(widget.courseId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with course image
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () {
                context.go('/courses');
              },
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  Image.network(
                    _courseData['imageUrl'],
                    fit: BoxFit.cover,
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _courseData['title'],
                          style: AppTheme.headingStyle.copyWith(
                            color: Colors.white,
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 12,
                              backgroundImage:
                                  NetworkImage(_courseData['instructorAvatar']),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _courseData['instructor'],
                              style: AppTheme.bodyStyle.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Course info
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Price and rating
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '\$${_courseData['price']}',
                        style: AppTheme.headingStyle.copyWith(
                          color: AppTheme.primaryColor,
                          fontSize: 24,
                        ),
                      ),
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber),
                          const SizedBox(width: 4),
                          Text(
                            '${_courseData['rating']} (${_courseData['reviewCount']} reviews)',
                            style: AppTheme.bodyStyle,
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Course stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(Icons.people_outline,
                          '${_courseData['students']} Students'),
                      _buildStatItem(Icons.play_circle_outline,
                          '${_courseData['lessons']} Lessons'),
                      _buildStatItem(Icons.access_time,
                          '${_courseData['duration']} Hours'),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Tab bar
                  TabBar(
                    controller: _tabController,
                    labelColor: AppTheme.primaryColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppTheme.primaryColor,
                    tabs: const [
                      Tab(text: 'Description'),
                      Tab(text: 'Curriculum'),
                      Tab(text: 'Reviews'),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Tab content
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Description tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'About This Course',
                        style: AppTheme.subheadingStyle,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _courseData['description'],
                        style: AppTheme.bodyStyle,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'What You\'ll Learn',
                        style: AppTheme.subheadingStyle,
                      ),
                      const SizedBox(height: 8),
                      ...(_courseData['learningPoints'] as List).map(
                        (point) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Icon(Icons.check_circle,
                                  color: AppTheme.primaryColor, size: 16),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  point,
                                  style: AppTheme.bodyStyle,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(
                          height: 100), // Space for the bottom button
                    ],
                  ),
                ),

                // Curriculum tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Course Content',
                        style: AppTheme.subheadingStyle,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${_courseData['lessons']} lessons • ${_courseData['duration']} hours total',
                        style: AppTheme.bodyStyle.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ...(_courseData['curriculum'] as List).map(
                        (section) => _buildCurriculumSection(section),
                      ),
                      const SizedBox(
                          height: 100), // Space for the bottom button
                    ],
                  ),
                ),

                // Reviews tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Reviews',
                            style: AppTheme.subheadingStyle,
                          ),
                          const Spacer(),
                          TextButton.icon(
                            onPressed: () {
                              context.go('/rating/${widget.courseId}');
                            },
                            icon: const Icon(Icons.star,
                                color: AppTheme.primaryColor),
                            label: const Text(
                              'Add Review',
                              style: TextStyle(color: AppTheme.primaryColor),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ...(_courseData['reviews'] as List).map(
                        (review) => _buildReviewItem(review),
                      ),
                      const SizedBox(
                          height: 100), // Space for the bottom button
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, -3),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            context.go('/course/${widget.courseId}/learn');
          },
          style: AppTheme.primaryButtonStyle,
          child: const Text('Enroll Now'),
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String text) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.primaryColor),
        const SizedBox(height: 4),
        Text(
          text,
          style: AppTheme.smallTextStyle,
        ),
      ],
    );
  }

  Widget _buildCurriculumSection(Map<String, dynamic> section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      section['title'],
                      style: AppTheme.subheadingStyle.copyWith(
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '${section['lessons']} lessons • ${section['duration']} min',
                      style: AppTheme.smallTextStyle,
                    ),
                  ],
                ),
              ),
              const Icon(Icons.keyboard_arrow_down),
            ],
          ),
        ),
        ...((section['items'] as List).map(
          (item) => Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            margin: const EdgeInsets.only(bottom: 8, left: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.play_circle_outline,
                    color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['title'],
                        style: AppTheme.bodyStyle,
                      ),
                      Text(
                        '${item['duration']} min',
                        style: AppTheme.smallTextStyle,
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.lock_outline, color: Colors.grey),
              ],
            ),
          ),
        )),
      ],
    );
  }

  Widget _buildReviewItem(Map<String, dynamic> review) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: NetworkImage(review['avatar']),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    review['name'],
                    style: AppTheme.bodyStyle.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    review['date'],
                    style: AppTheme.smallTextStyle,
                  ),
                ],
              ),
              const Spacer(),
              Row(
                children: [
                  const Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    review['rating'].toString(),
                    style: AppTheme.bodyStyle,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            review['comment'],
            style: AppTheme.bodyStyle,
          ),
        ],
      ),
    );
  }

  // Dummy data for a course
  Map<String, dynamic> _getDummyCourseData(String courseId) {
    // In a real app, this would fetch data from an API based on the courseId
    return {
      'id': courseId,
      'title': 'Flutter Complete Course: From Zero to Hero',
      'instructor': 'John Doe',
      'instructorAvatar': 'https://randomuser.me/api/portraits/men/32.jpg',
      'rating': 4.8,
      'reviewCount': 245,
      'price': 49.99,
      'imageUrl':
          'https://miro.medium.com/v2/resize:fit:1400/1*6JxdGU2WIzHSUEGBx4QeAQ.jpeg',
      'students': 1234,
      'lessons': 42,
      'duration': 12,
      'description':
          'Learn Flutter and Dart from the ground up, build beautiful, fast, native-quality iOS and Android apps. This course is designed for both beginners and experienced developers who want to learn Flutter from scratch or improve their skills. You\'ll learn all the fundamentals of Flutter development and build several apps throughout the course.',
      'learningPoints': [
        'Build beautiful, fast, and native-quality apps with Flutter',
        'Learn Dart from scratch',
        'Build engaging, complex, and responsive user interfaces',
        'Use state management techniques to build robust applications',
        'Integrate with Firebase for backend services',
        'Publish your apps to the Google Play Store and Apple App Store',
      ],
      'curriculum': [
        {
          'title': 'Section 1: Introduction to Flutter',
          'lessons': 5,
          'duration': 45,
          'items': [
            {'title': 'What is Flutter?', 'duration': 8},
            {
              'title': 'Setting up your development environment',
              'duration': 12
            },
            {'title': 'Creating your first Flutter app', 'duration': 10},
            {'title': 'Understanding the Flutter widget tree', 'duration': 8},
            {'title': 'Hot reload and hot restart', 'duration': 7},
          ],
        },
        {
          'title': 'Section 2: Dart Fundamentals',
          'lessons': 6,
          'duration': 60,
          'items': [
            {'title': 'Variables and data types', 'duration': 10},
            {'title': 'Control flow statements', 'duration': 12},
            {'title': 'Functions and methods', 'duration': 10},
            {'title': 'Classes and objects', 'duration': 12},
            {'title': 'Inheritance and mixins', 'duration': 8},
            {'title': 'Async programming with Futures', 'duration': 8},
          ],
        },
        {
          'title': 'Section 3: Flutter Widgets',
          'lessons': 8,
          'duration': 90,
          'items': [
            {'title': 'Stateless vs Stateful widgets', 'duration': 12},
            {'title': 'Layout widgets', 'duration': 15},
            {'title': 'Container and decoration', 'duration': 10},
            {'title': 'Lists and grids', 'duration': 12},
            {'title': 'Forms and input fields', 'duration': 12},
            {'title': 'Dialogs and alerts', 'duration': 10},
            {'title': 'Navigation and routing', 'duration': 10},
            {'title': 'Themes and styling', 'duration': 9},
          ],
        },
      ],
      'reviews': [
        {
          'name': 'Sarah Johnson',
          'avatar': 'https://randomuser.me/api/portraits/women/44.jpg',
          'rating': 5.0,
          'date': 'June 15, 2023',
          'comment':
              'This course is amazing! I had no prior experience with Flutter, but now I feel confident building my own apps. The instructor explains everything clearly and the projects are very practical.',
        },
        {
          'name': 'Michael Brown',
          'avatar': 'https://randomuser.me/api/portraits/men/46.jpg',
          'rating': 4.5,
          'date': 'May 22, 2023',
          'comment':
              'Great course with lots of practical examples. I especially liked the section on state management. The only thing I would suggest is adding more content on testing.',
        },
        {
          'name': 'Emily Davis',
          'avatar': 'https://randomuser.me/api/portraits/women/22.jpg',
          'rating': 5.0,
          'date': 'April 10, 2023',
          'comment':
              'I\'ve taken several Flutter courses, and this is by far the best one. The instructor is knowledgeable and explains concepts in a way that\'s easy to understand.',
        },
      ],
    };
  }
}
