import 'package:flutter/material.dart';
import 'package:dsdemy/theme/app_theme.dart';
import 'package:go_router/go_router.dart';

class UserProfileScreen extends StatelessWidget {
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Profile header with avatar and stats
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: const BoxDecoration(
                  color: AppTheme.primaryColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                    bottomRight: Radius.circular(30),
                  ),
                ),
                child: Column(
                  children: [
                    // Avatar
                    const CircleAvatar(
                      radius: 50,
                      backgroundImage: NetworkImage(
                        'https://randomuser.me/api/portraits/women/44.jpg',
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Name
                    Text(
                      '<PERSON>',
                      style: AppTheme.headingStyle.copyWith(
                        color: Colors.white,
                        fontSize: 24,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    // Email
                    Text(
                      '<EMAIL>',
                      style: AppTheme.bodyStyle.copyWith(
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 24),
                    
                    // Stats row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildStatColumn('Courses', '12'),
                        _buildStatColumn('Completed', '8'),
                        _buildStatColumn('Certificates', '5'),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Profile details
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Profile Details',
                      style: AppTheme.subheadingStyle,
                    ),
                    const SizedBox(height: 16),
                    
                    // Profile info cards
                    _buildProfileInfoCard(
                      icon: Icons.person_outline,
                      title: 'Name',
                      value: 'Jane Doe',
                    ),
                    _buildProfileInfoCard(
                      icon: Icons.email_outlined,
                      title: 'Email',
                      value: '<EMAIL>',
                    ),
                    _buildProfileInfoCard(
                      icon: Icons.phone_outlined,
                      title: 'Phone',
                      value: '****** 567 890',
                    ),
                    _buildProfileInfoCard(
                      icon: Icons.location_on_outlined,
                      title: 'Location',
                      value: 'New York, USA',
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Edit profile button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          context.go('/update-profile');
                        },
                        style: AppTheme.primaryButtonStyle,
                        child: const Text('Edit Profile'),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Change password button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          context.go('/update-password');
                        },
                        style: AppTheme.secondaryButtonStyle,
                        child: const Text('Change Password'),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Logout button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          context.go('/login');
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: const Text('Logout'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 4, // Profile tab
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.play_circle_outline),
            label: 'My Courses',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite_border),
            label: 'Wishlist',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            label: 'Profile',
          ),
        ],
        onTap: (index) {
          // Handle navigation
          if (index == 0) {
            context.go('/courses');
          } else if (index == 2) {
            context.go('/courses');
          } else if (index == 4) {
            // Already on profile
          }
        },
      ),
    );
  }
  
  Widget _buildStatColumn(String title, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTheme.headingStyle.copyWith(
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: AppTheme.smallTextStyle.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }
  
  Widget _buildProfileInfoCard({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTheme.smallTextStyle,
              ),
              Text(
                value,
                style: AppTheme.bodyStyle,
              ),
            ],
          ),
        ],
      ),
    );
  }
}