import 'package:flutter/material.dart';
import 'package:dsdemy/theme/app_theme.dart';
import 'package:go_router/go_router.dart';

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen> {
  int _selectedCategoryIndex = 0;
  final List<String> _categories = ['All', 'Popular', 'Newest', 'Flutter', 'Kotlin', 'Python', 'JavaScript'];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'DS',
                style: AppTheme.headingStyle.copyWith(
                  fontSize: 24,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: 'DEMY',
                style: AppTheme.headingStyle.copyWith(
                  fontSize: 24,
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Search functionality
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Categories
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategoryIndex = index;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: _selectedCategoryIndex == index
                          ? AppTheme.primaryColor
                          : Colors.grey[200],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      _categories[index],
                      style: TextStyle(
                        color: _selectedCategoryIndex == index
                            ? Colors.white
                            : Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Courses grid
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.7,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _dummyCourses.length,
              itemBuilder: (context, index) {
                final course = _dummyCourses[index];
                return _buildCourseCard(course);
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 0, // Home tab
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.play_circle_outline),
            label: 'My Courses',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite_border),
            label: 'Wishlist',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            label: 'Profile',
          ),
        ],
        onTap: (index) {
          if (index == 4) {
            context.go('/profile');
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go('/create-course');
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
  
  Widget _buildCourseCard(Map<String, dynamic> course) {
    return GestureDetector(
      onTap: () {
        context.go('/course/${course['id']}');
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Course image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: Image.network(
                course['imageUrl'],
                height: 100,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      course['category'],
                      style: AppTheme.smallTextStyle.copyWith(
                        fontSize: 10,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // Title
                  Text(
                    course['title'],
                    style: AppTheme.subheadingStyle.copyWith(
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  
                  // Instructor
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 10,
                        backgroundImage: NetworkImage(course['instructorAvatar']),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          course['instructor'],
                          style: AppTheme.smallTextStyle.copyWith(
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Rating and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 14),
                          const SizedBox(width: 2),
                          Text(
                            course['rating'].toString(),
                            style: AppTheme.smallTextStyle.copyWith(
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '\$${course['price']}',
                        style: AppTheme.bodyStyle.copyWith(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Dummy data for courses
  final List<Map<String, dynamic>> _dummyCourses = [
    {
      'id': '1',
      'title': 'Flutter Complete Course',
      'category': 'Flutter',
      'instructor': 'John Doe',
      'instructorAvatar': 'https://randomuser.me/api/portraits/men/32.jpg',
      'rating': 4.8,
      'price': 49.99,
      'imageUrl': 'https://miro.medium.com/v2/resize:fit:1400/1*6JxdGU2WIzHSUEGBx4QeAQ.jpeg',
    },
    {
      'id': '2',
      'title': 'Kotlin for Android Development',
      'category': 'Kotlin',
      'instructor': 'Jane Smith',
      'instructorAvatar': 'https://randomuser.me/api/portraits/women/44.jpg',
      'rating': 4.5,
      'price': 39.99,
      'imageUrl': 'https://developer.android.com/static/images/kotlin/kotlin-for-android-banner.jpg',
    },
    {
      'id': '3',
      'title': 'Python for Data Science',
      'category': 'Python',
      'instructor': 'Robert Johnson',
      'instructorAvatar': 'https://randomuser.me/api/portraits/men/46.jpg',
      'rating': 4.9,
      'price': 59.99,
      'imageUrl': 'https://www.freecodecamp.org/news/content/images/2022/02/Banner-1.png',
    },
    {
      'id': '4',
      'title': 'JavaScript Fundamentals',
      'category': 'JavaScript',
      'instructor': 'Emily Davis',
      'instructorAvatar': 'https://randomuser.me/api/portraits/women/22.jpg',
      'rating': 4.7,
      'price': 29.99,
      'imageUrl': 'https://www.pragimtech.com/wp-content/uploads/2020/08/javascript-tutorial.jpg',
    },
    {
      'id': '5',
      'title': 'Advanced Flutter Techniques',
      'category': 'Flutter',
      'instructor': 'Michael Brown',
      'instructorAvatar': 'https://randomuser.me/api/portraits/men/67.jpg',
      'rating': 4.6,
      'price': 69.99,
      'imageUrl': 'https://miro.medium.com/v2/resize:fit:1400/1*6JxdGU2WIzHSUEGBx4QeAQ.jpeg',
    },
    {
      'id': '6',
      'title': 'Kotlin Coroutines Mastery',
      'category': 'Kotlin',
      'instructor': 'Sarah Wilson',
      'instructorAvatar': 'https://randomuser.me/api/portraits/women/56.jpg',
      'rating': 4.4,
      'price': 49.99,
      'imageUrl': 'https://developer.android.com/static/images/kotlin/kotlin-for-android-banner.jpg',
    },
  ];
}